import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform

def test_mac_fonts():
    """简单测试Mac系统字体"""
    print("=== Mac系统字体测试 ===")
    print(f"系统: {platform.system()}")
    
    # 清除缓存并重建
    fm._rebuild()
    
    # 获取所有字体
    fonts = [f.name for f in fm.fontManager.ttflist]
    
    # Mac常见中文字体
    test_fonts = [
        'PingFang SC',
        'Hiragino Sans GB', 
        'STHeiti',
        'Arial Unicode MS',
        'Heiti SC'
    ]
    
    print("\n检查字体可用性:")
    available_fonts = []
    for font in test_fonts:
        if font in fonts:
            print(f"✓ {font} - 可用")
            available_fonts.append(font)
        else:
            print(f"✗ {font} - 不可用")
    
    if not available_fonts:
        print("\n搜索包含'PingFang'或'Hiragino'的字体:")
        for font in fonts:
            if 'PingFang' in font or 'Hiragino' in font:
                print(f"找到: {font}")
                available_fonts.append(font)
                break
    
    # 测试最佳字体
    if available_fonts:
        best_font = available_fonts[0]
        print(f"\n使用字体: {best_font}")
        
        # 配置matplotlib
        plt.rcParams['font.sans-serif'] = [best_font, 'Helvetica', 'Arial']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建测试图
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # 测试文本
        test_texts = [
            '中文字体测试',
            '盈利能力分析', 
            '偿债能力分析',
            '营运能力分析',
            '发展能力分析',
            '上海环境公司',
            '远达环保',
            '首创环保', 
            '龙净环保'
        ]
        
        # 绘制测试文本
        for i, text in enumerate(test_texts):
            y_pos = 0.9 - i * 0.1
            ax.text(0.1, y_pos, text, fontsize=14, transform=ax.transAxes)
            ax.text(0.6, y_pos, f"Font: {best_font}", fontsize=10, 
                   transform=ax.transAxes, style='italic', alpha=0.7)
        
        ax.set_title(f'Mac系统中文字体测试 - {best_font}', fontsize=16, fontweight='bold')
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        
        plt.tight_layout()
        plt.savefig('Mac字体测试结果.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"✅ 测试完成！图片已保存为: Mac字体测试结果.png")
        print(f"推荐配置: plt.rcParams['font.sans-serif'] = ['{best_font}', 'Helvetica']")
        
        return best_font
    else:
        print("❌ 未找到可用的中文字体")
        return None

if __name__ == "__main__":
    test_mac_fonts()
